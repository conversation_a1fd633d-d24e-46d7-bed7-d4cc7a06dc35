# Excel 图片解析改进说明

## 问题描述
原始的 `index2.vue` 文件在解析 Excel 文件中的图片时存在以下问题：
1. 图片位置解析不准确，无法正确定位到对应的单元格
2. 生成的 JSON 格式不够标准，结构复杂
3. 多工作表支持不完善

## 主要改进

### 1. 图片位置解析改进

#### 改进前的问题：
- 图片位置信息解析不准确
- 只支持双单元格锚点，不支持单单元格锚点
- 位置偏移信息被忽略

#### 改进后的功能：
- **支持多种锚点类型**：同时支持双单元格锚点（twoCellAnchor）和单单元格锚点（oneCellAnchor）
- **精确位置解析**：解析行列位置以及偏移信息
- **多工作表支持**：为每个工作表单独提取图片位置信息

```javascript
// 改进的位置解析函数
const parseDetailedImagePositions = (xmlContent) => {
  // 支持双单元格锚点和单单元格锚点
  const twoCellAnchors = xmlDoc.querySelectorAll("xdr\\:twoCellAnchor, twoCellAnchor");
  const oneCellAnchors = xmlDoc.querySelectorAll("xdr\\:oneCellAnchor, oneCellAnchor");
  
  // 解析位置和偏移信息
  positions.push({
    row: row,
    col: col,
    colOffset: colOff,
    rowOffset: rowOff,
    anchorType: "twoCell", // 或 "oneCell"
    anchorIndex: index,
  });
}
```

### 2. 图片数据处理改进

#### 改进前的问题：
- 图片按列名匹配分配，而不是按实际位置
- 图片位置信息没有被正确使用

#### 改进后的功能：
- **按实际位置放置图片**：根据解析出的行列位置将图片放置到对应单元格
- **位置验证**：确保图片位置在数据范围内
- **详细日志**：提供详细的调试信息

```javascript
// 改进的图片处理函数
const processDataWithExtractedImages = (jsonData, images, sheetName) => {
  // 根据图片的实际位置信息将图片放置到对应单元格
  images.forEach((image, index) => {
    if (image.position && image.position.row !== undefined && image.position.col !== undefined) {
      const targetRow = image.position.row;
      const targetCol = image.position.col;
      
      // 确保目标位置在数据范围内
      if (targetRow >= 0 && targetRow < processedData.length && 
          targetCol >= 0 && processedData[targetRow] && targetCol < processedData[targetRow].length) {
        processedData[targetRow][targetCol] = image.data;
      }
    }
  });
}
```

### 3. 多工作表支持改进

#### 改进前的问题：
- 所有工作表使用相同的图片数据
- 工作表关系文件路径固定为 sheet1

#### 改进后的功能：
- **独立处理每个工作表**：为每个工作表单独提取图片
- **智能路径解析**：支持多种工作表关系文件路径格式
- **工作表编号映射**：根据工作表名称智能确定编号

```javascript
// 为每个工作表单独提取图片位置信息
for (const sheetName of workbook.SheetNames) {
  const sheetImages = await extractImagesFromExcelForSheet(arrayBuffer, sheetName);
  // 处理当前工作表的图片数据
}

// 智能工作表编号映射
const getSheetNumber = (sheetName) => {
  if (sheetName === "Sheet1" || sheetName === "工作表1") return 1;
  if (sheetName === "Sheet2" || sheetName === "工作表2") return 2;
  // 尝试从名称中提取数字
  const match = sheetName.match(/(\d+)/);
  if (match) return parseInt(match[1]);
  return 1; // 默认返回1
};
```

### 4. JSON 格式标准化

#### 改进前的问题：
- JSON 结构复杂，包含过多元数据
- 图片数据格式不统一

#### 改进后的功能：
- **简化的 JSON 结构**：更加标准和简洁的数据格式
- **统一的图片数据格式**：图片数据包含类型、位置等标准信息
- **清晰的数据层次**：元数据、数据和摘要信息分离

```javascript
// 标准化的 JSON 格式
{
  "metadata": {
    "totalSheets": 1,
    "sheetNames": ["Sheet1"],
    "parseTime": "2025-01-XX...",
    "encoding": "UTF-8"
  },
  "data": {
    "Sheet1": {
      "headers": ["列1", "图片", "列3"],
      "rows": [
        {
          "列1": "数据1",
          "图片": {
            "type": "image",
            "data": "data:image/png;base64,...",
            "position": {
              "row": 1,
              "column": 2
            }
          },
          "列3": "数据3"
        }
      ],
      "summary": {
        "totalRows": 1,
        "totalColumns": 3,
        "imageCount": 1,
        "extractedImages": [...]
      }
    }
  }
}
```

## 使用方法

1. **上传 Excel 文件**：选择包含图片的 Excel 文件
2. **点击解析**：系统会自动解析所有工作表和图片
3. **查看结果**：
   - 表格中会显示图片（如果成功解析）
   - 控制台会输出详细的解析日志
   - JSON 数据会显示标准格式的结果

## 调试信息

系统会在控制台输出详细的调试信息，包括：
- 工作表处理进度
- 图片位置解析结果
- 图片文件提取状态
- 数据处理过程

## 注意事项

1. **Excel 文件格式**：支持 .xlsx 格式（不支持 .xls）
2. **图片格式**：支持常见的图片格式（PNG、JPG、GIF 等）
3. **位置精度**：图片位置基于 Excel 的锚点信息，可能存在微小偏差
4. **性能考虑**：大文件或包含大量图片的文件可能需要较长处理时间

## 技术细节

- **JSZip**：用于解析 Excel 文件的 ZIP 结构
- **xlsx**：用于解析工作表数据
- **DOMParser**：用于解析 XML 格式的绘图信息
- **Base64 编码**：图片数据以 Base64 格式存储和传输
