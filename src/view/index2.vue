<template>
  <div class="container">
    <div class="upload-section">
      <h2>Excel 文件解析器</h2>
      <input type="file" @change="handleFileUpload" accept=".xlsx, .xls" />
      <br />
      <button @click="parseExcel">解析 Excel</button>
    </div>

    <div v-if="fileContent.length > 0">
      <!-- 工作表选择器 -->
      <div v-if="availableSheets.length > 1" class="sheet-selector">
        <h3>选择工作表：</h3>
        <select v-model="currentSheet" @change="switchSheet">
          <option v-for="sheet in availableSheets" :key="sheet" :value="sheet">
            {{ sheet }}
          </option>
        </select>
      </div>

      <h3>文件内容：{{ currentSheet }}</h3>
      <table>
        <thead>
          <tr>
            <th v-for="(header, index) in headers" :key="index">
              {{ header || `列 ${index + 1}` }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(row, rowIndex) in fileContent" :key="rowIndex">
            <td v-for="(cell, cellIndex) in row" :key="cellIndex">
              <!-- 如果是图片单元格，显示图片 -->
              <img
                v-if="isImageCell(cell)"
                :src="cell"
                alt="图片"
                class="image-cell"
                @error="handleImageError"
              />
              <!-- 否则显示文本 -->
              <span v-else>{{ cell }}</span>
            </td>
          </tr>
        </tbody>
      </table>

      <!-- 显示解析后的JSON数据 -->
      <div v-if="parsedJsonData" class="json-section">
        <h3>解析后的JSON数据（UTF-8格式）：</h3>
        <pre>{{ formattedJsonData }}</pre>
        <div class="json-actions">
          <button @click="copyJsonToClipboard">复制JSON数据</button>
          <button @click="downloadJsonFile">下载JSON文件</button>
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script setup>
import { ref, computed } from "vue";
import { read, utils } from "xlsx";
import JSZip from "jszip";

const selectedFile = ref(null);
const fileContent = ref([]);
const headers = ref([]);
const parsedJsonData = ref(null);
const allSheetsData = ref({});
const currentSheet = ref("");
const availableSheets = ref([]);

const handleFileUpload = (event) => {
  selectedFile.value = event.target.files[0];
};

// 判断是否为图片单元格
const isImageCell = (cell) => {
  if (typeof cell !== "string") return false;

  // 检查是否为base64图片
  if (cell.startsWith("data:image/")) return true;

  // 检查是否为HTTP/HTTPS图片URL
  if (cell.match(/^https?:\/\/.*\.(jpg|jpeg|png|gif|bmp|webp|svg)(\?.*)?$/i))
    return true;

  // 检查是否为本地图片路径
  if (cell.match(/\.(jpg|jpeg|png|gif|bmp|webp|svg)$/i)) return true;

  // 检查是否包含图片相关关键词
  if (
    cell.toLowerCase().includes("image") ||
    cell.toLowerCase().includes("图片")
  )
    return true;

  return false;
};

// 格式化JSON数据用于显示
const formattedJsonData = computed(() => {
  if (!parsedJsonData.value) return "";
  return JSON.stringify(parsedJsonData.value, null, 2);
});

const parseExcel = async () => {
  if (!selectedFile.value) {
    alert("请先选择文件");
    return;
  }

  try {
    // 同时使用两种方法：JSZip提取图片 + xlsx解析数据
    const arrayBuffer = await selectedFile.value.arrayBuffer();

    // 方法1: 使用JSZip提取Excel文件中的图片
    const images = await extractImagesFromExcel(arrayBuffer);

    // 方法2: 使用xlsx解析数据
    const data = new Uint8Array(arrayBuffer);
    const workbook = read(data, {
      type: "array",
      cellHTML: false,
      cellNF: false,
      cellText: false,
      cellDates: true,
      bookImages: true,
      cellStyles: true,
    });

    // 获取所有工作表名称
    availableSheets.value = workbook.SheetNames;
    currentSheet.value = workbook.SheetNames[0];

    // 解析所有工作表
    const allData = {};

    // 为每个工作表单独提取图片位置信息
    for (const sheetName of workbook.SheetNames) {
      const worksheet = workbook.Sheets[sheetName];

      // 解析为JSON数据
      const jsonData = utils.sheet_to_json(worksheet, {
        header: 1,
        defval: "",
        blankrows: false,
        raw: false,
      });

      if (jsonData.length > 0) {
        // 为当前工作表提取图片位置信息
        console.log(`开始处理工作表: ${sheetName}`);
        const sheetImages = await extractImagesFromExcelForSheet(
          arrayBuffer,
          sheetName
        );

        // 处理图片数据
        const processedData = processDataWithExtractedImages(
          jsonData,
          sheetImages,
          sheetName
        );

        allData[sheetName] = {
          headers: jsonData[0],
          data: jsonData.slice(1),
          processedData: processedData,
          images: sheetImages,
        };

        console.log(
          `工作表 ${sheetName} 处理完成，提取到 ${sheetImages.length} 张图片`
        );
      }
    }

    allSheetsData.value = allData;

    // 显示第一个工作表的数据
    if (allData[currentSheet.value]) {
      headers.value = allData[currentSheet.value].headers;
      fileContent.value = allData[currentSheet.value].processedData.slice(1);

      // 生成结构化的JSON数据（包含所有工作表）
      const structuredData = generateStructuredJsonForAllSheets(allData);
      parsedJsonData.value = structuredData;

      // 以UTF-8格式打印JSON数据到控制台
      console.log("=== 解析后的JSON数据（UTF-8格式）===");
      console.log(JSON.stringify(structuredData, null, 2));
      console.log("=== JSON数据结束 ===");

      // 如果提取到了图片，在控制台显示
      if (images.length > 0) {
        console.log("=== 提取到的图片信息 ===");
        images.forEach((img, index) => {
          console.log(
            `图片 ${index + 1}:`,
            img.name,
            `大小: ${img.data.length} bytes`
          );
        });
      }
    }
  } catch (error) {
    console.error("解析 Excel 文件出错:", error);
    alert("解析文件时出错: " + error.message);
  }
};

// 工作表切换功能
const switchSheet = () => {
  if (allSheetsData.value[currentSheet.value]) {
    headers.value = allSheetsData.value[currentSheet.value].headers;
    fileContent.value =
      allSheetsData.value[currentSheet.value].processedData.slice(1);
  }
};

// 使用JSZip从Excel文件中提取图片（旧版本，保持兼容性）
const extractImagesFromExcel = async (arrayBuffer) => {
  return await extractImagesFromExcelForSheet(arrayBuffer, "Sheet1");
};

// 为特定工作表从Excel文件中提取图片（增强调试版本）
const extractImagesFromExcelForSheet = async (arrayBuffer, sheetName) => {
  const images = [];

  try {
    const zip = await JSZip.loadAsync(arrayBuffer);
    console.log(`🔍 开始为工作表 "${sheetName}" 提取图片...`);

    // 首先列出ZIP文件中的所有文件，用于调试
    console.log("📁 ZIP文件结构:");
    zip.forEach((relativePath, file) => {
      if (
        relativePath.includes("media") ||
        relativePath.includes("image") ||
        relativePath.includes("drawing") ||
        relativePath.includes(".png") ||
        relativePath.includes(".jpg") ||
        relativePath.includes(".jpeg")
      ) {
        console.log(`  - ${relativePath} (${file.dir ? "目录" : "文件"})`);
      }
    });

    // 1. 不再依赖复杂的位置解析，直接按顺序分配到第三列
    console.log(`📍 图片将按顺序分配到第三列（列索引2）`);

    // 2. 提取图片文件 - 尝试多个可能的路径
    let mediaFiles = [];

    // 尝试标准路径
    const mediaFolder = zip.folder("xl/media");
    if (mediaFolder) {
      mediaFolder.forEach((relativePath, file) => {
        if (!file.dir) {
          mediaFiles.push({
            path: relativePath,
            file: file,
            source: "xl/media",
          });
        }
      });
    }

    // 如果标准路径没有找到图片，尝试其他可能的路径
    if (mediaFiles.length === 0) {
      console.log("⚠️  xl/media 文件夹为空，尝试其他路径...");

      // 尝试查找所有可能的图片文件
      zip.forEach((relativePath, file) => {
        if (
          !file.dir &&
          (relativePath.match(/\.(png|jpg|jpeg|gif|bmp|svg)$/i) ||
            relativePath.includes("image") ||
            relativePath.includes("picture"))
        ) {
          mediaFiles.push({
            path: relativePath,
            file: file,
            source: "alternative",
          });
          console.log(`  找到替代图片文件: ${relativePath}`);
        }
      });
    }

    // 3. 按顺序将图片分配到第三列
    console.log(
      `📊 找到 ${mediaFiles.length} 个图片文件，将按顺序分配到第三列`
    );

    if (mediaFiles.length > 0) {
      // 为每个图片文件分配位置信息
      for (let i = 0; i < mediaFiles.length; i++) {
        const mediaFile = mediaFiles[i];
        try {
          const imageData = await mediaFile.file.async("base64");
          const extension = mediaFile.path.split(".").pop().toLowerCase();

          let mimeType = "image/png";
          if (extension === "jpg" || extension === "jpeg") {
            mimeType = "image/jpeg";
          } else if (extension === "gif") {
            mimeType = "image/gif";
          } else if (extension === "bmp") {
            mimeType = "image/bmp";
          } else if (extension === "svg") {
            mimeType = "image/svg+xml";
          }

          // 按行顺序分配图片位置（第三列固定）
          // 图片按顺序对应数据行：第i张图片对应第i行数据的第三列（列索引2）
          const position = {
            row: i, // Excel行号从0开始，第i张图片对应第i行
            col: 2, // 第三列，列索引为2
            colOffset: 0,
            rowOffset: 0,
            anchorType: "fixed",
            anchorIndex: i,
          };

          console.log(
            `📍 图片 ${mediaFile.path} (第${i + 1}张) 分配到位置: 行${
              position.row
            }, 列${position.col} (第三列)`
          );

          images.push({
            name: mediaFile.path,
            data: `data:${mimeType};base64,${imageData}`,
            extension: extension,
            type: "embedded",
            position: position,
            index: i,
            sheetName: sheetName,
          });

          console.log(
            `✅ 工作表 ${sheetName} - 图片 ${mediaFile.path} 位置: 行${position.row}, 列${position.col} (第三列)`
          );
        } catch (error) {
          console.warn(`❌ 提取图片 ${mediaFile.path} 失败:`, error);
        }
      }
    } else {
      console.warn(`⚠️  工作表 ${sheetName} 没有找到任何图片文件`);
    }

    console.log(`从工作表 ${sheetName} 中提取到 ${images.length} 张图片`);
  } catch (error) {
    console.warn(`JSZip提取工作表 ${sheetName} 图片失败:`, error);
  }

  return images;
};

// 更精确的图片位置提取方法 - 支持多工作表
const extractImagePositionsFromWorkbook = async (zip, sheetName) => {
  const imagePositions = [];

  try {
    console.log(`开始提取工作表 "${sheetName}" 的图片位置信息`);

    // 1. 确定工作表编号（假设按顺序命名）
    const sheetNumber = getSheetNumber(sheetName);
    const relsPath = `xl/worksheets/_rels/sheet${sheetNumber}.xml.rels`;

    console.log(`尝试读取关系文件: ${relsPath}`);
    const relsFile = zip.file(relsPath);

    if (relsFile) {
      const relsContent = await relsFile.async("text");
      console.log(`成功读取关系文件，内容长度: ${relsContent.length}`);

      const parser = new DOMParser();
      const relsDoc = parser.parseFromString(relsContent, "text/xml");

      // 查找绘图关系
      const relationships = relsDoc.getElementsByTagName("Relationship");
      console.log(`找到 ${relationships.length} 个关系`);

      for (let i = 0; i < relationships.length; i++) {
        const rel = relationships[i];
        const type = rel.getAttribute("Type");
        const target = rel.getAttribute("Target");

        console.log(`关系 ${i + 1}: 类型=${type}, 目标=${target}`);

        if (type && type.includes("drawing") && target) {
          // 读取对应的绘图文件
          const drawingPath = `xl/worksheets/${target}`;
          console.log(`尝试读取绘图文件: ${drawingPath}`);

          const drawingFile = zip.file(drawingPath);

          if (drawingFile) {
            const drawingContent = await drawingFile.async("text");
            console.log(`成功读取绘图文件，内容长度: ${drawingContent.length}`);

            const positions = parseDetailedImagePositions(drawingContent);
            imagePositions.push(...positions);
            console.log(`从绘图文件中解析到 ${positions.length} 个位置`);
          } else {
            console.warn(`绘图文件不存在: ${drawingPath}`);
          }
        }
      }
    } else {
      console.warn(`关系文件不存在: ${relsPath}`);

      // 尝试其他可能的路径
      const alternativePaths = [
        `xl/worksheets/_rels/sheet1.xml.rels`,
        `xl/worksheets/_rels/${sheetName.toLowerCase()}.xml.rels`,
      ];

      for (const altPath of alternativePaths) {
        const altFile = zip.file(altPath);
        if (altFile) {
          console.log(`找到替代关系文件: ${altPath}`);
          // 使用相同的处理逻辑
          const relsContent = await altFile.async("text");
          const parser = new DOMParser();
          const relsDoc = parser.parseFromString(relsContent, "text/xml");
          const relationships = relsDoc.getElementsByTagName("Relationship");

          for (let i = 0; i < relationships.length; i++) {
            const rel = relationships[i];
            const type = rel.getAttribute("Type");
            const target = rel.getAttribute("Target");

            if (type && type.includes("drawing") && target) {
              const drawingPath = `xl/worksheets/${target}`;
              const drawingFile = zip.file(drawingPath);

              if (drawingFile) {
                const drawingContent = await drawingFile.async("text");
                const positions = parseDetailedImagePositions(drawingContent);
                imagePositions.push(...positions);
              }
            }
          }
          break;
        }
      }
    }

    console.log(
      `工作表 "${sheetName}" 总共提取到 ${imagePositions.length} 个图片位置`
    );
  } catch (error) {
    console.warn("提取图片位置信息失败:", error);
  }

  return imagePositions;
};

// 辅助函数：根据工作表名称获取编号
const getSheetNumber = (sheetName) => {
  // 简单的映射逻辑，可以根据实际需要调整
  if (sheetName === "Sheet1" || sheetName === "工作表1") return 1;
  if (sheetName === "Sheet2" || sheetName === "工作表2") return 2;
  if (sheetName === "Sheet3" || sheetName === "工作表3") return 3;

  // 尝试从名称中提取数字
  const match = sheetName.match(/(\d+)/);
  if (match) {
    return parseInt(match[1]);
  }

  // 默认返回1
  return 1;
};

// 详细解析图片位置 - 改进版本
const parseDetailedImagePositions = (xmlContent) => {
  const positions = [];

  try {
    const parser = new DOMParser();
    const xmlDoc = parser.parseFromString(xmlContent, "text/xml");

    // 查找所有的双单元格锚点和单单元格锚点
    const twoCellAnchors = xmlDoc.querySelectorAll(
      "xdr\\:twoCellAnchor, twoCellAnchor"
    );
    const oneCellAnchors = xmlDoc.querySelectorAll(
      "xdr\\:oneCellAnchor, oneCellAnchor"
    );

    // 处理双单元格锚点
    twoCellAnchors.forEach((anchor, index) => {
      try {
        // 获取起始位置
        const fromElement = anchor.querySelector("xdr\\:from, from");
        if (fromElement) {
          const colElement = fromElement.querySelector("xdr\\:col, col");
          const rowElement = fromElement.querySelector("xdr\\:row, row");
          const colOffElement = fromElement.querySelector(
            "xdr\\:colOff, colOff"
          );
          const rowOffElement = fromElement.querySelector(
            "xdr\\:rowOff, rowOff"
          );

          if (colElement && rowElement) {
            const col = parseInt(colElement.textContent);
            const row = parseInt(rowElement.textContent);
            const colOff = colOffElement
              ? parseInt(colOffElement.textContent)
              : 0;
            const rowOff = rowOffElement
              ? parseInt(rowOffElement.textContent)
              : 0;

            positions.push({
              row: row, // 保持原始行号，不需要+1，因为Excel内部行号从0开始
              col: col, // 保持原始列号
              colOffset: colOff,
              rowOffset: rowOff,
              anchorType: "twoCell",
              anchorIndex: index,
            });

            console.log(
              `解析到双单元格锚点图片位置: 行${row}, 列${col} (偏移: 行${rowOff}, 列${colOff})`
            );
          }
        }
      } catch (error) {
        console.warn(`解析第${index}个双单元格锚点失败:`, error);
      }
    });

    // 处理单单元格锚点
    oneCellAnchors.forEach((anchor, index) => {
      try {
        const fromElement = anchor.querySelector("xdr\\:from, from");
        if (fromElement) {
          const colElement = fromElement.querySelector("xdr\\:col, col");
          const rowElement = fromElement.querySelector("xdr\\:row, row");
          const colOffElement = fromElement.querySelector(
            "xdr\\:colOff, colOff"
          );
          const rowOffElement = fromElement.querySelector(
            "xdr\\:rowOff, rowOff"
          );

          if (colElement && rowElement) {
            const col = parseInt(colElement.textContent);
            const row = parseInt(rowElement.textContent);
            const colOff = colOffElement
              ? parseInt(colOffElement.textContent)
              : 0;
            const rowOff = rowOffElement
              ? parseInt(rowOffElement.textContent)
              : 0;

            positions.push({
              row: row,
              col: col,
              colOffset: colOff,
              rowOffset: rowOff,
              anchorType: "oneCell",
              anchorIndex: index,
            });

            console.log(
              `解析到单单元格锚点图片位置: 行${row}, 列${col} (偏移: 行${rowOff}, 列${colOff})`
            );
          }
        }
      } catch (error) {
        console.warn(`解析第${index}个单单元格锚点失败:`, error);
      }
    });

    console.log(`总共解析到 ${positions.length} 个图片位置信息`);
  } catch (error) {
    console.warn("解析绘图XML失败:", error);
  }

  return positions;
};

// 处理提取到的图片数据 - 根据实际位置放置图片（修复版本）
const processDataWithExtractedImages = (jsonData, images, sheetName) => {
  const processedData = JSON.parse(JSON.stringify(jsonData));

  if (processedData.length === 0) return processedData;

  console.log(`处理工作表 ${sheetName} 的图片数据，共 ${images.length} 张图片`);
  console.log(
    `数据表格大小: ${processedData.length} 行 x ${
      processedData[0]?.length || 0
    } 列`
  );

  // 创建一个位置映射，记录哪些位置已经放置了图片
  const imagePositionMap = new Map();

  // 首先，根据图片的实际位置信息将图片放置到对应单元格
  images.forEach((image, index) => {
    if (
      image.position &&
      image.position.row !== undefined &&
      image.position.col !== undefined
    ) {
      // Excel 坐标转换为数组索引
      // Excel 的行列都是从0开始的，但我们的数据第0行是表头
      // 所以图片的行位置需要+1来跳过表头
      const targetRow = image.position.row + 1; // +1 跳过表头行
      const targetCol = image.position.col;

      console.log(
        `图片 ${index + 1} (${image.name}): Excel位置[${image.position.row}, ${
          image.position.col
        }] -> 数组位置[${targetRow}, ${targetCol}]`
      );

      // 确保目标位置在数据范围内
      if (
        targetRow >= 0 &&
        targetRow < processedData.length &&
        targetCol >= 0 &&
        processedData[targetRow] &&
        targetCol < processedData[targetRow].length
      ) {
        // 将图片数据放置到对应位置
        processedData[targetRow][targetCol] = image.data;

        // 记录这个位置已经有图片了
        imagePositionMap.set(`${targetRow}-${targetCol}`, true);

        console.log(
          `✅ 成功将图片 ${image.name} 放置到数组位置 [${targetRow}, ${targetCol}]`
        );
        console.log(`   图片数据长度: ${image.data.length} 字符`);
      } else {
        console.warn(
          `❌ 图片 ${image.name} 的目标位置 [${targetRow}, ${targetCol}] 超出数据范围`
        );
        console.warn(
          `   数据范围: ${processedData.length} 行 x ${
            processedData[0]?.length || 0
          } 列`
        );
      }
    } else {
      console.warn(`⚠️  图片 ${image.name || index} 没有有效的位置信息`);
      console.warn(`   位置数据:`, image.position);
    }
  });

  // 其次，为明确标识为图片列但没有图片的单元格生成占位符
  // 注意：只有在确实没有图片数据的情况下才生成占位符
  const headers = processedData[0];
  headers.forEach((header, colIndex) => {
    if (
      header &&
      (header.toLowerCase().includes("图") ||
        header.toLowerCase().includes("image") ||
        header.toLowerCase().includes("配图") ||
        header.toLowerCase().includes("picture"))
    ) {
      console.log(`发现图片列: "${header}" (列索引: ${colIndex})`);

      // 检查这一列的每个单元格
      for (let rowIndex = 1; rowIndex < processedData.length; rowIndex++) {
        const currentValue = processedData[rowIndex][colIndex];
        const positionKey = `${rowIndex}-${colIndex}`;

        // 只有在单元格为空且没有图片数据的情况下才生成占位符
        if (
          (!currentValue || currentValue === "") &&
          !imagePositionMap.has(positionKey)
        ) {
          // 生成占位符，表示这里应该有图片但未能提取
          processedData[rowIndex][
            colIndex
          ] = `[图片缺失 ${rowIndex}-${colIndex}]`;
          console.log(
            `📝 为位置 [${rowIndex}, ${colIndex}] 生成占位符（无图片数据）`
          );
        } else if (currentValue && currentValue.startsWith("data:image/")) {
          console.log(`🖼️  位置 [${rowIndex}, ${colIndex}] 已有图片数据`);
        }
      }
    }
  });

  console.log(`工作表 ${sheetName} 图片处理完成`);
  return processedData;
};

// 从工作表中提取图片
const extractImagesFromSheet = async (sheet) => {
  const images = [];

  try {
    // xlsx-populate 提供了访问图片的方法
    // const workbook = sheet.workbook(); // 暂时不使用

    // 获取工作表中的所有图片
    // 注意：xlsx-populate 的图片访问可能需要特殊处理
    const sheetData = sheet._node;

    if (sheetData && sheetData.children) {
      // 查找绘图部分
      const drawing = sheetData.children.find(
        (child) => child.name === "drawing"
      );

      if (drawing && drawing.attributes && drawing.attributes["r:id"]) {
        // 这里需要进一步处理图片数据
        // 由于 xlsx-populate 的限制，我们使用一个简化的方法
        console.log("发现图片引用:", drawing.attributes["r:id"]);
      }
    }

    // 作为备选方案，我们扫描单元格寻找可能的图片信息
    const usedRange = sheet.usedRange();
    if (usedRange) {
      const startRow = usedRange.startCell().rowNumber();
      const endRow = usedRange.endCell().rowNumber();
      const startCol = usedRange.startCell().columnNumber();
      const endCol = usedRange.endCell().columnNumber();

      for (let row = startRow; row <= endRow; row++) {
        for (let col = startCol; col <= endCol; col++) {
          const cell = sheet.cell(row, col);

          // 检查单元格是否有超链接
          if (cell.hyperlink()) {
            const link = cell.hyperlink();
            if (typeof link === "string" && isImageCell(link)) {
              images.push({
                row: row,
                col: col,
                data: link,
                type: "hyperlink",
              });
            }
          }

          // 检查单元格值是否是图片URL
          const value = cell.value();
          if (value && typeof value === "string" && isImageCell(value)) {
            images.push({
              row: row,
              col: col,
              data: value,
              type: "url",
            });
          }
        }
      }
    }
  } catch (error) {
    console.warn("图片提取失败:", error);
  }

  return images;
};

// 处理真实图片数据
const processDataWithRealImages = (jsonData, images) => {
  const processedData = JSON.parse(JSON.stringify(jsonData));

  if (processedData.length === 0) return processedData;

  // 将图片数据插入到对应位置
  images.forEach((image) => {
    const rowIndex = image.row - 1; // 转换为0基索引
    const colIndex = image.col - 1;

    if (
      processedData[rowIndex] &&
      processedData[rowIndex][colIndex] !== undefined
    ) {
      processedData[rowIndex][colIndex] = image.data;
    }
  });

  // 检查图片列，为空的单元格生成占位符
  const headers = processedData[0];
  headers.forEach((header, colIndex) => {
    if (
      header &&
      (header.toLowerCase().includes("图") ||
        header.toLowerCase().includes("image") ||
        header.toLowerCase().includes("配图") ||
        header.toLowerCase().includes("picture"))
    ) {
      // 这是图片列
      for (let rowIndex = 1; rowIndex < processedData.length; rowIndex++) {
        if (
          !processedData[rowIndex][colIndex] ||
          processedData[rowIndex][colIndex] === ""
        ) {
          // 检查是否有对应的图片
          const hasImage = images.some(
            (img) => img.row - 1 === rowIndex && img.col - 1 === colIndex
          );

          if (!hasImage) {
            // 生成一个模拟的图片数据URL（用于演示）
            processedData[rowIndex][colIndex] =
              generatePlaceholderImage(rowIndex);
          }
        }
      }
    }
  });

  return processedData;
};

// 生成占位符图片（一个简单的base64编码的1x1像素图片）
const generatePlaceholderImage = (index) => {
  // 创建一个简单的彩色1x1像素图片
  const colors = [
    "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==", // 红色
    "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNk+M9QDwADhgGAWjR9awAAAABJRU5ErkJggg==", // 绿色
    "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYGBgAAAABQABpfZFQwAAAABJRU5ErkJggg==", // 蓝色
  ];

  const colorIndex = index % colors.length;
  return `data:image/png;base64,${colors[colorIndex]}`;
};

// 处理图片占位符的函数
const processDataWithImagePlaceholders = (jsonData, worksheet) => {
  const processedData = JSON.parse(JSON.stringify(jsonData));

  if (processedData.length === 0) return processedData;

  const headers = processedData[0];

  // 查找图片列
  headers.forEach((header, colIndex) => {
    if (
      header &&
      (header.toLowerCase().includes("图") ||
        header.toLowerCase().includes("image") ||
        header.toLowerCase().includes("配图") ||
        header.toLowerCase().includes("picture"))
    ) {
      // 这是图片列，为每一行生成图片占位符
      for (let rowIndex = 1; rowIndex < processedData.length; rowIndex++) {
        const cellValue = processedData[rowIndex][colIndex];

        if (!cellValue || cellValue === "") {
          // 尝试从worksheet中获取更多信息
          const cellAddress = utils.encode_cell({ r: rowIndex, c: colIndex });
          const cell = worksheet[cellAddress];

          if (cell) {
            // 检查是否有超链接
            if (cell.l && cell.l.Target) {
              processedData[rowIndex][colIndex] = cell.l.Target;
            }
            // 检查是否有注释
            else if (cell.c && cell.c.length > 0) {
              const comment = cell.c[0];
              if (comment.t && isImageCell(comment.t)) {
                processedData[rowIndex][colIndex] = comment.t;
              } else {
                processedData[rowIndex][colIndex] = `[图片:${
                  comment.t || "未知"
                }]`;
              }
            }
            // 检查是否有公式
            else if (cell.f) {
              processedData[rowIndex][colIndex] = `[图片公式:${cell.f}]`;
            }
            // 默认占位符
            else {
              processedData[rowIndex][colIndex] = `[图片${rowIndex}]`;
            }
          } else {
            // 没有单元格数据，使用默认占位符
            processedData[rowIndex][colIndex] = `[图片${rowIndex}]`;
          }
        } else if (!isImageCell(cellValue)) {
          // 如果有值但不是图片，可能是图片描述
          processedData[rowIndex][colIndex] = `[图片:${cellValue}]`;
        }
      }
    }
  });

  return processedData;
};

// 提取Excel中的图片信息
const extractImages = (workbook, sheetName) => {
  const images = [];
  try {
    // 方法1: 尝试从workbook中提取图片信息
    if (workbook.Sheets[sheetName]["!images"]) {
      images.push(...workbook.Sheets[sheetName]["!images"]);
    }

    // 方法2: 检查workbook级别的图片
    if (workbook.Workbook && workbook.Workbook.Sheets) {
      const sheetInfo = workbook.Workbook.Sheets.find(
        (s) => s.name === sheetName
      );
      if (sheetInfo && sheetInfo.images) {
        images.push(...sheetInfo.images);
      }
    }

    // 方法3: 扫描所有单元格，查找可能的图片URL或路径
    const worksheet = workbook.Sheets[sheetName];
    const range = utils.decode_range(worksheet["!ref"] || "A1:A1");

    for (let row = range.s.r; row <= range.e.r; row++) {
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = utils.encode_cell({ r: row, c: col });
        const cell = worksheet[cellAddress];

        if (cell && cell.v && typeof cell.v === "string") {
          // 检查是否为图片URL或路径
          if (isImageCell(cell.v)) {
            images.push({
              position: { row, col },
              data: cell.v,
              type: "url",
            });
          }
        }
      }
    }

    // 方法4: 检查单元格的超链接和注释
    for (let row = range.s.r; row <= range.e.r; row++) {
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = utils.encode_cell({ r: row, c: col });
        const cell = worksheet[cellAddress];

        if (cell) {
          // 检查超链接
          if (cell.l && cell.l.Target && isImageCell(cell.l.Target)) {
            images.push({
              position: { row, col },
              data: cell.l.Target,
              type: "hyperlink",
            });
          }

          // 检查注释中的图片
          if (cell.c && Array.isArray(cell.c)) {
            cell.c.forEach((comment) => {
              if (comment.t && isImageCell(comment.t)) {
                images.push({
                  position: { row, col },
                  data: comment.t,
                  type: "comment",
                });
              }
            });
          }
        }
      }
    }
  } catch (error) {
    console.warn("图片提取失败:", error);
  }
  return images;
};

// 处理数据，将图片信息合并到对应单元格
const processDataWithImages = (jsonData, images, worksheet) => {
  const processedData = JSON.parse(JSON.stringify(jsonData));

  // 如果有图片信息，将其合并到对应的单元格
  images.forEach((image) => {
    if (image.position && image.data) {
      const row = image.position.row;
      const col = image.position.col;

      // 确保行和列在数据范围内
      if (processedData[row] && col < processedData[row].length) {
        if (
          image.type === "url" ||
          image.type === "hyperlink" ||
          image.type === "comment"
        ) {
          // 直接使用URL
          processedData[row][col] = image.data;
        } else if (image.data && image.ext) {
          // 将图片数据转换为base64格式
          processedData[row][
            col
          ] = `data:image/${image.ext};base64,${image.data}`;
        }
      }
    }
  });

  // 额外检查：查找可能包含图片信息但为空的单元格
  if (worksheet) {
    const range = utils.decode_range(worksheet["!ref"] || "A1:A1");
    for (let row = range.s.r; row <= range.e.r; row++) {
      for (let col = range.s.c; col <= range.e.c; col++) {
        const cellAddress = utils.encode_cell({ r: row, c: col });
        const cell = worksheet[cellAddress];

        if (cell && processedData[row] && col < processedData[row].length) {
          // 检查单元格是否有图片但显示为空
          if (
            (!processedData[row][col] || processedData[row][col] === "") &&
            (cell.l || cell.c || cell.f)
          ) {
            // 尝试从超链接获取图片
            if (cell.l && cell.l.Target) {
              processedData[row][col] = cell.l.Target;
            }
            // 或者标记为可能包含图片的单元格
            else if (
              col > 0 &&
              processedData[0] &&
              processedData[0][col] &&
              (processedData[0][col].toString().toLowerCase().includes("图") ||
                processedData[0][col]
                  .toString()
                  .toLowerCase()
                  .includes("image"))
            ) {
              processedData[row][col] = "[图片数据未能提取]";
            }
          }
        }
      }
    }
  }

  return processedData;
};

// 生成标准的JSON数据格式
const generateStructuredJsonForAllSheets = (allData) => {
  const result = {
    metadata: {
      totalSheets: Object.keys(allData).length,
      sheetNames: Object.keys(allData),
      parseTime: new Date().toISOString(),
      encoding: "UTF-8",
    },
    data: {},
  };

  Object.keys(allData).forEach((sheetName) => {
    const sheetData = allData[sheetName];
    const headers = sheetData.headers;
    const rows = sheetData.processedData.slice(1); // 使用处理过的数据

    // 生成标准的表格数据格式
    const standardData = rows.map((row, rowIndex) => {
      const rowData = {};

      headers.forEach((header, colIndex) => {
        const cellValue = row[colIndex];
        const headerName = header || `Column_${colIndex + 1}`;

        // 直接使用单元格值，保持简洁的JSON结构
        if (cellValue !== undefined && cellValue !== null && cellValue !== "") {
          // 如果是图片数据，保留完整的base64数据
          if (isImageCell(cellValue)) {
            rowData[headerName] = {
              type: "image",
              data: cellValue,
              position: {
                row: rowIndex + 1,
                column: colIndex + 1,
              },
            };
          } else {
            // 普通数据直接存储值
            rowData[headerName] = cellValue;
          }
        } else {
          // 空值处理
          if (
            headerName.toLowerCase().includes("图") ||
            headerName.toLowerCase().includes("image") ||
            headerName.toLowerCase().includes("配图") ||
            headerName.toLowerCase().includes("picture")
          ) {
            // 图片列的空值标记
            rowData[headerName] = {
              type: "image",
              data: null,
              status: "missing",
              position: {
                row: rowIndex + 1,
                column: colIndex + 1,
              },
            };
          } else {
            // 普通列的空值可以省略或设为null
            rowData[headerName] = null;
          }
        }
      });

      return rowData;
    });

    result.data[sheetName] = {
      headers: headers,
      rows: standardData,
      summary: {
        totalRows: rows.length,
        totalColumns: headers.length,
        imageCount: sheetData.images.length,
        extractedImages: sheetData.images.map((img) => ({
          name: img.name,
          position: img.position,
          hasData: !!img.data,
        })),
      },
    };
  });

  return result;
};

// 检测单元格数据类型
const detectCellType = (value) => {
  if (value === null || value === undefined || value === "") return "empty";
  if (typeof value === "number") return "number";
  if (typeof value === "boolean") return "boolean";
  if (value instanceof Date) return "date";
  if (isImageCell(value)) return "image";
  return "string";
};

// 复制JSON数据到剪贴板
const copyJsonToClipboard = async () => {
  if (!parsedJsonData.value) return;

  try {
    const jsonString = JSON.stringify(parsedJsonData.value, null, 2);
    await navigator.clipboard.writeText(jsonString);
    alert("JSON数据已复制到剪贴板！");
  } catch (error) {
    console.error("复制失败:", error);
    alert("复制失败，请手动复制");
  }
};

// 下载JSON文件
const downloadJsonFile = () => {
  if (!parsedJsonData.value) return;

  const jsonString = JSON.stringify(parsedJsonData.value, null, 2);
  const blob = new Blob([jsonString], {
    type: "application/json;charset=utf-8",
  });
  const url = URL.createObjectURL(blob);

  const link = document.createElement("a");
  link.href = url;
  link.download = `excel_data_${new Date().getTime()}.json`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);
};

// 处理图片加载错误
const handleImageError = (event) => {
  console.warn("图片加载失败:", event.target.src);
  event.target.style.display = "none";
  // 在图片位置显示错误信息
  const errorText = document.createElement("span");
  errorText.textContent = "图片加载失败";
  errorText.style.color = "#999";
  errorText.style.fontSize = "12px";
  event.target.parentNode.appendChild(errorText);
};
</script>

  
  <style scoped>
.container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.upload-section {
  margin-bottom: 20px;
  padding: 20px;
  border: 2px dashed #ddd;
  border-radius: 8px;
  text-align: center;
}

.upload-section input[type="file"] {
  margin-bottom: 10px;
}

.upload-section button {
  background-color: #409eff;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
}

.upload-section button:hover {
  background-color: #337ecc;
}

.sheet-selector {
  margin-bottom: 20px;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.sheet-selector h3 {
  margin: 0 0 10px 0;
  color: #495057;
  font-size: 16px;
}

.sheet-selector select {
  padding: 8px 12px;
  border: 1px solid #ced4da;
  border-radius: 4px;
  background-color: white;
  font-size: 14px;
  min-width: 200px;
}

table {
  border-collapse: collapse;
  width: 100%;
  margin-bottom: 20px;
}

th,
td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
  vertical-align: top;
}

th {
  background-color: #f2f2f2;
  font-weight: bold;
}

.json-section {
  margin-top: 20px;
  padding: 20px;
  border: 1px solid #ddd;
  border-radius: 8px;
  background-color: #fafafa;
}

.json-section h3 {
  margin-top: 0;
  color: #333;
}

.json-section pre {
  max-height: 400px;
  overflow-y: auto;
  font-size: 12px;
  line-height: 1.4;
}

.json-actions {
  margin-top: 10px;
}

.json-actions button {
  margin-right: 10px;
  padding: 8px 16px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  cursor: pointer;
}

.json-actions button:hover {
  background-color: #f0f0f0;
}

.image-cell {
  max-width: 100px;
  max-height: 100px;
  object-fit: contain;
  border-radius: 4px;
}
</style>
